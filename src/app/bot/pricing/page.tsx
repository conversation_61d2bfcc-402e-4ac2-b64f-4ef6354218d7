"use client";

import { Check, Star, Users, DollarSign, Crown } from 'lucide-react';
import { Navigation } from "@/components/sections/navigation";
import { Footer } from "@/components/sections/footer";
import { Animated3DElements } from '@/components/ui/animated-3d-elements';
import { Particle3DSystem } from '@/components/ui/floating-3d-icons';

export default function PricingPage() {

  const includedFeatures = [
    "Expired fans follower",
    "New fans follower",
    "Cross-Follower/SFS Tool",
    "Mobile App Access",
    "Telegram and Email Support"
  ];

  const pricingPlans = [
    {
      name: "Solo",
      monthlyPrice: 198,
      yearlyPrice: 2376,
      description: "Perfect for solo creators and smaller agencies looking to get started with OFAutoFollower.",
      popular: false,
      features: includedFeatures
    },
    {
      name: "Agency",
      monthlyPrice: 149,
      yearlyPrice: 1788,
      description: "Are you an Agency that's looking to onboard more than 10 accounts? Enjoy cheaper prices when you buy 10+ licenses.",
      popular: true,
      features: includedFeatures
    }
  ];

  return (
    <main className="min-h-screen">
      <Navigation />

      {/* Hero Section */}
      <section className="bg-white min-h-screen flex items-center pt-20 relative overflow-hidden">
        {/* 3D Background Elements */}
        <Animated3DElements variant="minimal" />
        <Particle3DSystem particleCount={8} />

        <div className="max-w-7xl mx-auto px-6 lg:px-8 text-center relative">

          {/* Header */}
          <div className="mb-16">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-primary/10 border border-primary/20 text-sm font-medium text-primary mb-8">
              <DollarSign className="w-4 h-4" />
              Simple Pricing
            </div>

            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-4 md:mb-6 px-4">
              Choose Your
              <br />
              <span className="gradient-text">Perfect Plan</span>
            </h1>

            <p className="text-base sm:text-lg md:text-xl text-gray-600 mb-8 md:mb-12 max-w-3xl mx-auto px-4">
              Whether you&apos;re a solo creator or managing an agency, we have the right plan to maximize your OnlyFans revenue.
            </p>
          </div>

          {/* Pricing Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8 max-w-6xl mx-auto mb-12 md:mb-16 px-4">
            {pricingPlans.map((plan, index) => (
              <div
                key={index}
                className={`relative card-bg rounded-2xl md:rounded-3xl p-6 md:p-8 border-2 transition-all duration-300 ${
                  plan.popular
                    ? 'border-primary shadow-lg shadow-primary/20'
                    : 'border-gray-200 hover:border-primary/40'
                }`}
              >
                {/* Popular Badge */}
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <div className="bg-primary text-white px-6 py-2 rounded-full text-sm font-semibold flex items-center gap-2">
                      <Crown className="w-4 h-4" />
                      Popular
                    </div>
                  </div>
                )}

                {/* Plan Header */}
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">{plan.name}</h3>

                  <div className="mb-6">
                    {plan.name === 'Agency' && (
                      <div className="flex items-center justify-center gap-3 mb-4">
                        <span className="text-2xl text-gray-500 line-through">$198</span>
                        <div className="bg-green-500/20 text-green-600 px-3 py-1 rounded-full text-sm font-semibold">
                          Save $49/month
                        </div>
                      </div>
                    )}
                    <span className="text-5xl md:text-6xl font-bold text-gray-900">${plan.monthlyPrice}</span>
                    <span className="text-xl text-gray-600 ml-2">/month</span>
                  </div>

                  <div className="text-sm text-gray-600 mb-4">
                    (${plan.yearlyPrice}/year)
                  </div>

                  <p className="text-gray-600 text-sm leading-relaxed">
                    {plan.description}
                  </p>
                </div>

                {/* CTA Buttons */}
                <div className="mb-8 space-y-3">
                  <a
                    href="https://app.ofautofollower.com/register"
                    target="_blank"
                    className={`w-full font-bold px-6 py-4 rounded-xl inline-flex items-center gap-3 shadow-lg justify-center text-lg transition-all duration-300 ${
                      plan.popular
                        ? 'premium-button text-white hover-lift'
                        : 'bg-gray-100 hover:bg-gray-200 text-gray-900 hover:shadow-xl'
                    }`}
                  >
                    Sign Up
                  </a>
                </div>

                {/* Features */}
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-900 mb-4">What&apos;s Included:</h4>
                  <div className="space-y-3">
                    {plan.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center gap-3">
                        <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                          <Check className="w-3 h-3 text-white" />
                        </div>
                        <span className="text-gray-700">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Trust Indicators */}
          <div className="flex items-center justify-center gap-8 mb-12 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <div className="flex text-yellow-400">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 fill-current" />
                ))}
              </div>
              <span>4.7/5 rating</span>
            </div>
            <span className="hidden md:block">•</span>
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              <span>1,000+ creators</span>
            </div>
            <span className="hidden md:block">•</span>
            <div className="text-green-600 font-medium">
              Cancel anytime
            </div>
          </div>

          {/* Social proof stats */}
          <div className="grid grid-cols-3 gap-8 max-w-2xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold text-green-500 mb-2">$2M+</div>
              <div className="text-gray-600 text-sm">Revenue recovered</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-2">50K+</div>
              <div className="text-gray-600 text-sm">Fans recovered</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-2">99%</div>
              <div className="text-gray-600 text-sm">Uptime</div>
            </div>
          </div>
        </div>
      </section>

      {/* Simple FAQ */}
      <section className="py-20 section-bg">
        <div className="max-w-3xl mx-auto px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">
            Common Questions
          </h2>
          
          <div className="space-y-6">
            {[
              {
                q: "Is this safe for my OnlyFans account?",
                a: "Yes, we use human-like behavior patterns and stay within OnlyFans limits. 1,000+ creators use our tool safely."
              },
              {
                q: "How quickly will I see results?",
                a: "Most creators see new followers within 24 hours and increased revenue within the first week."
              },
              {
                q: "Can I cancel anytime?",
                a: "Absolutely. Cancel your subscription anytime with no cancellation fees or penalties."
              },
              {
                q: "Do you offer support?",
                a: "Yes, we provide 24/7 email support to help you maximize your results."
              }
            ].map((faq, index) => (
              <div key={index} className="bg-white p-6 rounded-xl border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">{faq.q}</h3>
                <p className="text-gray-600">{faq.a}</p>
              </div>
            ))}
          </div>
        </div>
      </section>
      
      <Footer />
    </main>
  );
} 