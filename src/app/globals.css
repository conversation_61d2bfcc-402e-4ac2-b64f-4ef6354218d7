@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 100%;
  --foreground: 0 0% 15%;
  --card: 0 0% 98%;
  --card-foreground: 0 0% 15%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 15%;
  --primary: 195 100% 47%;
  --primary-foreground: 0 0% 100%;
  --secondary: 0 0% 96%;
  --secondary-foreground: 0 0% 15%;
  --muted: 0 0% 96%;
  --muted-foreground: 0 0% 45%;
  --accent: 195 100% 47%;
  --accent-foreground: 0 0% 100%;
  --destructive: 0 85% 60%;
  --destructive-foreground: 0 0% 98%;
  --border: 0 0% 90%;
  --input: 0 0% 96%;
  --ring: 195 100% 47%;
  --radius: 0.75rem;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

/* Prevent media elements from causing horizontal overflow */
img, video, iframe {
  max-width: 100%;
  height: auto;
}

html {
  overflow-x: hidden;
  overflow-y: auto;
  scroll-behavior: smooth;
  font-feature-settings: 'cv11', 'ss01';
  font-variation-settings: 'opsz' 32;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #ffffff;
  color: hsl(var(--foreground));
}

body {
  position: relative;
  min-height: 100vh;
}

@layer base {
  h1, h2, h3, h4, h5, h6 {
    font-feature-settings: 'cv11', 'ss01';
    font-variation-settings: 'opsz' 32;
    color: hsl(var(--foreground));
  }
}

@layer components {
  .text-balance {
    text-wrap: balance;
  }
  
  .gradient-bg {
    background: #00aff0;
  }

  .gradient-text {
    color: #00aff0;
  }

  .subtle-gradient-text {
    color: #00aff0;
  }

  .primary-glow {
    background: rgba(0, 175, 240, 0.1);
  }
  
  @keyframes gradientShift {
    0%, 100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }
  
  @keyframes float {
    0%, 100% {
      transform: translateY(0px) scale(1);
    }
    50% {
      transform: translateY(-20px) scale(1.02);
    }
  }
  
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes glow {
    0%, 100% {
      box-shadow: 0 0 20px rgba(0, 175, 240, 0.4);
    }
    50% {
      box-shadow: 0 0 40px rgba(0, 175, 240, 0.6);
    }
  }
  
  @keyframes pulse-ring {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    100% {
      transform: scale(1.5);
      opacity: 0;
    }
  }
  
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  .animate-glow {
    animation: glow 3s ease-in-out infinite;
  }
  
  .animate-fadeInUp {
    animation: fadeInUp 0.8s ease-out forwards;
    opacity: 0;
  }
  
  .hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .hover-lift:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 175, 240, 0.2);
  }

  .premium-button {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: #00aff0;
  }

  .premium-button:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    transition: left 0.6s;
  }

  .premium-button:hover:before {
    left: 100%;
  }
  
  .premium-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 35px rgba(0, 175, 240, 0.3);
  }
  
  .glass-card {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(0, 175, 240, 0.15);
  }

  .hero-gradient {
    background: #ffffff;
  }

  .glass-effect {
    background: rgba(0, 175, 240, 0.05);
    border: 1px solid rgba(0, 175, 240, 0.2);
  }
  
  .card-shadow {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  }

  .card-shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
  
  .btn-primary {
    @apply bg-primary hover:bg-primary/90 text-white font-medium px-6 py-3 rounded-xl transition-all duration-200 inline-flex items-center justify-center gap-2 shadow-lg hover:shadow-primary/25;
  }

  .btn-secondary {
    @apply bg-gray-100/80 hover:bg-gray-200/80 text-gray-900 font-medium px-6 py-3 rounded-xl border border-blue-500/20 transition-all duration-200 inline-flex items-center justify-center gap-2;
  }
  
  .section-bg {
    background: #f8fafc;
  }

  .card-bg {
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(0, 175, 240, 0.15);
  }

  .neon-border {
    position: relative;
    border: 1px solid rgba(0, 175, 240, 0.5);
    box-shadow: 0 0 20px rgba(0, 175, 240, 0.3);
  }
  
  /* 3D Animation Keyframes */
  @keyframes rotate3d {
    0% {
      transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg);
    }
    33% {
      transform: rotateX(15deg) rotateY(45deg) rotateZ(0deg);
    }
    66% {
      transform: rotateX(-10deg) rotateY(90deg) rotateZ(5deg);
    }
    100% {
      transform: rotateX(0deg) rotateY(135deg) rotateZ(0deg);
    }
  }

  @keyframes float3d {
    0%, 100% {
      transform: translateY(0px) translateZ(0px) rotateX(0deg);
    }
    25% {
      transform: translateY(-15px) translateZ(10px) rotateX(5deg);
    }
    50% {
      transform: translateY(-25px) translateZ(20px) rotateX(0deg);
    }
    75% {
      transform: translateY(-15px) translateZ(10px) rotateX(-5deg);
    }
  }

  @keyframes orbit {
    0% {
      transform: rotate(0deg) translateX(100px) rotate(0deg);
    }
    100% {
      transform: rotate(360deg) translateX(100px) rotate(-360deg);
    }
  }

  @keyframes morphShape {
    0%, 100% {
      border-radius: 50% 50% 50% 50%;
      transform: scale(1) rotate(0deg);
    }
    25% {
      border-radius: 60% 40% 60% 40%;
      transform: scale(1.1) rotate(90deg);
    }
    50% {
      border-radius: 40% 60% 40% 60%;
      transform: scale(0.9) rotate(180deg);
    }
    75% {
      border-radius: 70% 30% 70% 30%;
      transform: scale(1.05) rotate(270deg);
    }
  }

  @keyframes perspective-tilt {
    0%, 100% {
      transform: perspective(1000px) rotateX(0deg) rotateY(0deg);
    }
    25% {
      transform: perspective(1000px) rotateX(5deg) rotateY(10deg);
    }
    50% {
      transform: perspective(1000px) rotateX(0deg) rotateY(20deg);
    }
    75% {
      transform: perspective(1000px) rotateX(-5deg) rotateY(10deg);
    }
  }

  /* 3D Animation Classes */
  .animate-rotate3d {
    animation: rotate3d 20s linear infinite;
    transform-style: preserve-3d;
  }

  .animate-float3d {
    animation: float3d 8s ease-in-out infinite;
    transform-style: preserve-3d;
  }

  .animate-orbit {
    animation: orbit 15s linear infinite;
  }

  .animate-morph {
    animation: morphShape 12s ease-in-out infinite;
  }

  .animate-perspective-tilt {
    animation: perspective-tilt 10s ease-in-out infinite;
  }

  /* 3D Card Effects */
  .card-3d {
    transform-style: preserve-3d;
    transition: all 0.6s cubic-bezier(0.23, 1, 0.320, 1);
    perspective: 1000px;
  }

  .card-3d:hover {
    transform: perspective(1000px) rotateX(5deg) rotateY(10deg) translateZ(20px);
  }

  .card-3d::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 175, 240, 0.1), rgba(0, 175, 240, 0.05));
    border-radius: inherit;
    transform: translateZ(-1px);
    transition: all 0.6s ease;
  }

  .card-3d:hover::before {
    background: linear-gradient(135deg, rgba(0, 175, 240, 0.2), rgba(0, 175, 240, 0.1));
    transform: translateZ(-1px) scale(1.02);
  }

  /* 3D Geometric Shapes */
  .shape-3d {
    position: relative;
    transform-style: preserve-3d;
  }

  .shape-3d::before,
  .shape-3d::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: inherit;
  }

  .shape-3d::before {
    background: rgba(0, 175, 240, 0.3);
    transform: translateZ(-10px) scale(0.95);
  }

  .shape-3d::after {
    background: rgba(0, 175, 240, 0.1);
    transform: translateZ(-20px) scale(0.9);
  }

  /* Promote AOS-animated elements to their own compositing layer so the GPU
     can handle the transforms efficiently and scrolling stays smooth. */
  [data-aos] {
    will-change: opacity, transform;
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
  }

  /* Default visible state - ensures elements are always visible as fallback */
  [data-aos],
  body.aos-fallback-visible [data-aos] {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }

  /* Only apply initial hidden state when AOS is properly initialized and not in fallback mode */
  body.aos-init:not(.aos-fallback-visible) [data-aos="fade-up"]:not(.aos-animate) {
    transform: translate3d(0, 20px, 0);
    opacity: 0;
  }

  /* Animated state */
  [data-aos="fade-up"].aos-animate {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }

  /* Responsive utilities */
  @media (max-width: 640px) {
    .container {
      padding-left: 1rem;
      padding-right: 1rem;
    }

    /* Ensure text is readable on mobile */
    h1, h2, h3 {
      line-height: 1.2;
    }

    /* Improve button spacing on mobile */
    .premium-button {
      padding: 0.875rem 1.5rem !important;
      font-size: 1rem !important;
    }

    /* Better mobile card spacing */
    .card-bg {
      margin-bottom: 1rem;
    }

    /* Reduce 3D effects on mobile for better performance */
    .card-3d:hover {
      transform: perspective(1000px) rotateX(2deg) rotateY(5deg) translateZ(10px);
    }
  }

  /* Tablet responsive improvements */
  @media (min-width: 641px) and (max-width: 1024px) {
    .premium-button {
      padding: 1rem 2rem;
    }
  }
}
