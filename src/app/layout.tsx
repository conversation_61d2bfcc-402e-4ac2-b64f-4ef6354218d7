import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import "aos/dist/aos.css";
import { AOSInit } from "@/components/aos-init";
import Script from "next/script";

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
  preload: true,
});

export const metadata: Metadata = {
  metadataBase: new URL('https://ofautofollower.com'),
  title: "OF Auto Follower - Recover Expired Fans & Grow Your OnlyFans",
  description: "Auto-follow expired fans and new OF users to maximize your revenue. The #1 growth automation tool for OnlyFans creators. Recover lost earnings with one click.",
  keywords: "onlyfans auto follower, of auto follower, onlyfans expired fans, onlyfans automation, onlyfans growth tool, onlyfans bot, expired fans recovery",
  openGraph: {
    title: "OF Auto Follower - Recover Expired Fans & Grow Your OnlyFans",
    description: "The ultimate OnlyFans automation tool. Auto-follow expired fans and new users instantly. Maximize your revenue and grow your subscriber base. 100% SAFE.",
    url: "https://ofautofollower.com/",
    siteName: "OF AutoFollower",
    images: [
      {
        url: "/og-image.png",
        width: 1200,
        height: 630,
        alt: "OF AutoFollower - Maximize Your OnlyFans Reach With AI AutoFollower",
        type: "image/png",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "OF Auto Follower - Recover Expired Fans & Grow Your OnlyFans",
    description: "Auto-follow new OF users and reconnect with expired VIP fans. Cross-promote between accounts for explosive growth.",
    images: "/og-image.png",
    creator: "@ofautofollower",
    site: "@ofautofollower",
  },
  icons: {
    icon: [
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
    ],
    shortcut: '/favicon.ico',
    apple: '/apple-touch-icon.png',
  },
  other: {
    'twitter:card': 'summary_large_image',
    'twitter:site': '@ofautofollower',
    'twitter:creator': '@ofautofollower',
    'twitter:title': 'OF Auto Follower - Recover Expired Fans & Grow Your OnlyFans',
    'twitter:description': 'Auto-follow new OF users and reconnect with expired VIP fans. Cross-promote between accounts for explosive growth.',
    'twitter:image': 'https://ofautofollower.com/og-image.png',
    'twitter:image:alt': 'OF AutoFollower - Maximize Your OnlyFans Reach With AI AutoFollower',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`overflow-x-hidden ${inter.variable}`}>
      <body className={`${inter.className} antialiased`}>

        <AOSInit />
        {children}
        
        {/* Google Analytics - Moved to end of body for better performance */}
        {/* <Script
          src="https://www.googletagmanager.com/gtag/js?id=UA-*********-10"
          strategy="afterInteractive"
        />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'UA-*********-10');
            gtag('config', 'AW-320170390');
          `}
        </Script> */}
        
        {/* Plausible Analytics */}
        <Script 
          defer 
          data-domain="ofautofollower.com" 
          src="https://plausible.io/js/plausible.js"
          strategy="afterInteractive"
        />
      </body>
    </html>
  );
}
