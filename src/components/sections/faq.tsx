"use client";

import { useState } from 'react';
import { ChevronDown } from 'lucide-react';

const faqs = [
  {
    question: "Are there any limits on how many people I can follow?",
    answer: "No, there are no limits on our side. We stay within OnlyFans' limits to keep your account safe. You can contact OnlyFans support to increase your follow limits if needed."
  },
  {
    question: "Does this tool find ALL my expired fans?",
    answer: "Yes, it finds all expired fans but only follows those with active accounts and free subscription options to ensure efficiency and avoid unnecessary charges."
  },
  {
    question: "Can my assistants/chatters use the extension?",
    answer: "Yes! You don't need separate licenses. Your team can install the extension and use it on your OnlyFans account."
  },
  {
    question: "How long does the process take?",
    answer: "Usually 5-15 minutes. We use human-like behavior patterns to ensure your account stays safe, making this the fastest and most secure tool available."
  },
  {
    question: "Is there customer support available?",
    answer: "Yes! We provide 24/7 support via email. We&apos;re here to help you succeed."
  },
  {
    question: "Is my OnlyFans account safe?",
    answer: "Absolutely. We use human-like behavior patterns and stay within OnlyFans' rate limits. Thousands of creators have been using our tool safely for years."
  }
];

export function FAQ() {
  const [openItems, setOpenItems] = useState<number[]>([]);

  const toggleItem = (index: number) => {
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  return (
    <section id="faq" className="py-24 section-bg">
      <div className="max-w-4xl mx-auto px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Frequently asked questions
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                      Everything you need to know about ofautofollower.com. Can&apos;t find the answer you&apos;re looking for?
          <a href="mailto:<EMAIL>" className="text-primary hover:text-primary/80 font-medium"> Reach out to our support team</a>.
          </p>
        </div>
        
        {/* FAQ Items */}
        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <div
              key={index}
              className="bg-white rounded-2xl border border-gray-200 hover:border-primary/40 hover:shadow-lg transition-all duration-200 overflow-hidden"
            >
              <button
                className="w-full text-left p-6 focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-inset"
                onClick={() => toggleItem(index)}
              >
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900 pr-4">
                    {faq.question}
                  </h3>
                  <ChevronDown
                    className={`w-5 h-5 text-gray-600 transition-transform duration-200 flex-shrink-0 ${
                      openItems.includes(index) ? 'rotate-180' : ''
                    }`}
                  />
                </div>
              </button>

              <div className={`transition-all duration-200 ease-in-out ${
                openItems.includes(index)
                  ? 'max-h-96 opacity-100'
                  : 'max-h-0 opacity-0'
              } overflow-hidden`}>
                <div className="px-6 pb-6">
                  <div className="text-gray-600 leading-relaxed border-t border-gray-200 pt-4">
                    {faq.answer}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA - Updated to Light Theme */}
        <div className="text-center mt-16">
          <div className="bg-white rounded-2xl p-8 border border-gray-200 shadow-lg">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Still have questions?
            </h3>
            <p className="text-gray-600 mb-6">
              Our support team is available 24/7 to help you get started and answer any questions.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center justify-center px-6 py-3 rounded-lg bg-primary text-white font-medium hover:bg-primary/90 transition-colors"
              >
                ✉️ Email Support
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
} 