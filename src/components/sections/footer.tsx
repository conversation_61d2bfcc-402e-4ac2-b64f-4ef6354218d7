"use client";
import Image from "next/image";
import Link from "next/link";
import { Mail, Twitter, Instagram, Send } from 'lucide-react';
import { useState, useEffect } from 'react';

export function Footer() {
  const [currentYear, setCurrentYear] = useState<number | null>(null);

  useEffect(() => {
    setCurrentYear(new Date().getFullYear());
  }, []);
  return (
    <footer className="relative py-16 bg-gray-50 border-t border-gray-200">
      
      <div className="relative max-w-7xl mx-auto px-6 lg:px-8">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="md:col-span-1">
            <Link href="/" className="flex items-center gap-3 hover:opacity-80 transition-opacity mb-4" aria-label="OF AutoFollower - Home">
              <Image 
                src="/OFAFlogo.png" 
                alt="OF AutoFollower Logo" 
                width={45} 
                height={45}
                className="w-11 h-11"
                priority={false}
              />
              <span className="text-xl font-bold bg-gradient-to-r from-gray-900 to-primary bg-clip-text text-transparent">
                ofautofollower
              </span>
            </Link>
            <p className="text-gray-600 text-sm leading-relaxed">
              The most trusted automation tool for OnlyFans creators. Grow your subscriber base and maximize your revenue safely.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-gray-900 font-semibold mb-4">Quick Links</h3>
            <div className="space-y-2">
              <Link href="/#features" className="block text-gray-600 hover:text-primary transition-colors text-sm">Features</Link>
              <Link href="/bot/pricing" className="block text-gray-600 hover:text-primary transition-colors text-sm">Pricing</Link>
              <Link href="/#faq" className="block text-gray-600 hover:text-primary transition-colors text-sm">FAQ</Link>
              <Link href="/affiliate" className="block text-gray-600 hover:text-primary transition-colors text-sm">Affiliate Program</Link>
            </div>
          </div>

          {/* Legal */}
          <div>
            <h3 className="text-gray-900 font-semibold mb-4">Legal</h3>
            <div className="space-y-2">
              <Link href="/privacy-policy" className="block text-gray-600 hover:text-primary transition-colors text-sm">Privacy Policy</Link>
              <Link href="/terms" className="block text-gray-600 hover:text-primary transition-colors text-sm">Terms of Service</Link>
              <Link href="/refund" className="block text-gray-600 hover:text-primary transition-colors text-sm">Refund Policy</Link>
            </div>
          </div>

          {/* Contact */}
          <div>
            <h3 className="text-gray-900 font-semibold mb-4">Get in Touch</h3>
            <div className="space-y-3">
              <a
                href="mailto:<EMAIL>"
                className="flex items-center gap-2 text-gray-600 hover:text-primary transition-colors text-sm"
              >
                <Mail className="w-4 h-4" />
                <EMAIL>
              </a>
              <a
                href="https://t.me/onlyfansautofollower"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2 text-gray-600 hover:text-primary transition-colors text-sm"
              >
                <Send className="w-4 h-4" />
                @onlyfansautofollower
              </a>
              <div className="flex gap-3 mt-4">
                <a
                  href="https://t.me/onlyfansautofollower"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-2 bg-gray-100 rounded-lg hover:bg-primary/10 text-gray-600 hover:text-primary transition-colors"
                  aria-label="Join our Telegram"
                >
                  <Send className="w-4 h-4" />
                </a>
                <a
                  href="https://twitter.com/ofautofollower"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-2 bg-gray-100 rounded-lg hover:bg-primary/10 text-gray-600 hover:text-primary transition-colors"
                  aria-label="Follow us on Twitter"
                >
                  <Twitter className="w-4 h-4" />
                </a>
                <a
                  href="https://instagram.com/onlyfanstraffic"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-2 bg-gray-100 rounded-lg hover:bg-primary/10 text-gray-600 hover:text-primary transition-colors"
                  aria-label="Follow us on Instagram"
                >
                  <Instagram className="w-4 h-4" />
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom section */}
        <div className="mt-12 pt-8 border-t border-gray-200">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="text-gray-500 text-sm">
              &copy; {currentYear || 2024} ofautofollower.com. All rights reserved.
            </p>
            <p className="text-gray-500 text-sm">
              Made with ❤️ for OnlyFans creators
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
} 