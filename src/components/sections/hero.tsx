"use client";

import { useState, useEffect } from 'react';
import { ChevronRight, Star, Sparkles, Users, TrendingUp } from 'lucide-react';
import { Animated3DElements } from '@/components/ui/animated-3d-elements';
import { FloatingIconsSet, Animated3DGrid, Morphing3DBlob } from '@/components/ui/floating-3d-icons';

export function Hero() {
  const [isHovered, setIsHovered] = useState(false);
  const [revenueCount, setRevenueCount] = useState(0);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    // Set client flag to avoid hydration issues
    setIsClient(true);

    // Small delay to ensure component is fully mounted
    const startAnimation = () => {
      const target = 3500000;
      const duration = 3000;
      const increment = target / (duration / 16);

      let current = 0;
      const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
          setRevenueCount(target);
          clearInterval(timer);
        } else {
          setRevenueCount(Math.floor(current));
        }
      }, 16);

      return timer;
    };

    // Start animation after a short delay to ensure smooth rendering
    const animationTimeout = setTimeout(() => {
      const timer = startAnimation();
      return () => clearInterval(timer);
    }, 100);

    return () => {
      clearTimeout(animationTimeout);
    };
  }, []);

  return (
    <section className="hero-gradient min-h-[90vh] flex items-center relative overflow-hidden pt-20 md:pt-32">
      {/* Enhanced 3D Background Elements */}
      <Animated3DElements variant="hero" />
      <FloatingIconsSet variant="hero" />
      <Animated3DGrid />

      {/* Additional Morphing Blobs */}
      <Morphing3DBlob
        className="absolute top-16 right-[20%]"
        size="lg"
        color="primary"
      />
      <Morphing3DBlob
        className="absolute bottom-24 left-[15%]"
        size="md"
        color="secondary"
      />

      <div className="relative max-w-7xl mx-auto px-6 lg:px-8 w-full">
        {/* Centered Main Content */}
        <div className="text-center mb-16">
          {/* Badge */}
          <div className="inline-flex items-center gap-2 px-3 py-2 md:px-4 md:py-2 rounded-full bg-primary/10 border border-primary/20 text-xs md:text-sm font-medium text-primary mb-6 md:mb-8">
            <Sparkles className="w-3 h-3 md:w-4 md:h-4" />
            <span className="hidden sm:inline">{isClient ? `$${revenueCount.toLocaleString()} of revenue unlocked` : '$3,500,000 of revenue unlocked'}</span>
            <span className="sm:hidden">{isClient ? `$${(revenueCount/1000000).toFixed(1)}M unlocked` : '$3.5M unlocked'}</span>
          </div>

          {/* Main heading */}
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-4 md:mb-6 tracking-tight leading-tight max-w-4xl mx-auto px-4" style={{animationDelay: '0.2s'}}>
            Maximize Your OnlyFans Reach With <span className="text-primary">AI AutoFollower</span>
          </h1>

          {/* Subheading */}
          <p className="text-base sm:text-lg md:text-xl text-gray-600 mb-6 md:mb-8 leading-relaxed max-w-2xl mx-auto px-4" style={{animationDelay: '0.4s'}}>
            Auto-follow new OF users and reconnect with expired VIP fans. Cross-promote between your accounts for explosive growth and maximize your revenue potential.
          </p>

          {/* CTA buttons */}
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 items-center justify-center mb-8 md:mb-12 px-4" style={{animationDelay: '0.6s'}}>
            <a
              href="https://app.ofautofollower.com/register"
              target="_blank"
              className="group premium-button text-white font-semibold px-8 py-4 rounded-full inline-flex items-center gap-3 shadow-neon hover-lift"
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
            >
              <svg xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" viewBox="0 0 48 48" height="24" width="24" className="w-6 h-6">
                <defs>
                  <linearGradient id="a" x1="3.2173" y1="15" x2="44.7812" y2="15" gradientUnits="userSpaceOnUse">
                    <stop offset="0" stopColor="#d93025"/>
                    <stop offset="1" stopColor="#ea4335"/>
                  </linearGradient>
                  <linearGradient id="b" x1="20.7219" y1="47.6791" x2="41.5039" y2="11.6837" gradientUnits="userSpaceOnUse">
                    <stop offset="0" stopColor="#fcc934"/>
                    <stop offset="1" stopColor="#fbbc04"/>
                  </linearGradient>
                  <linearGradient id="c" x1="26.5981" y1="46.5015" x2="5.8161" y2="10.506" gradientUnits="userSpaceOnUse">
                    <stop offset="0" stopColor="#1e8e3e"/>
                    <stop offset="1" stopColor="#34a853"/>
                  </linearGradient>
                </defs>
                <circle cx="24" cy="23.9947" r="12" style={{fill:'#fff'}}/>
                <path d="M3.2154,36A24,24,0,1,0,12,3.2154,24,24,0,0,0,3.2154,36ZM34.3923,18A12,12,0,1,1,18,13.6077,12,12,0,0,1,34.3923,18Z" style={{fill:'none'}}/>
                <path d="M24,12H44.7812a23.9939,23.9939,0,0,0-41.5639.0029L13.6079,30l.0093-.0024A11.9852,11.9852,0,0,1,24,12Z" style={{fill:'url(#a)'}}/>
                <circle cx="24" cy="24" r="9.5" style={{fill:'#1a73e8'}}/>
                <path d="M34.3913,30.0029,24.0007,48A23.994,23.994,0,0,0,44.78,12.0031H23.9989l-.0025.0093A11.985,11.985,0,0,1,34.3913,30.0029Z" style={{fill:'url(#b)'}}/>
                <path d="M13.6086,30.0031,3.218,12.006A23.994,23.994,0,0,0,24.0025,48L34.3931,30.0029l-.0067-.0068a11.9852,11.9852,0,0,1-20.7778.007Z" style={{fill:'url(#c)'}}/>
              </svg>
              Sign Up Now
              <ChevronRight className={`w-5 h-5 transition-transform duration-200 ${isHovered ? 'translate-x-1' : ''}`} />
            </a>

            <button className="flex items-center gap-3 px-6 py-4 text-gray-700 hover:text-primary transition-colors group">
              <div className="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center group-hover:shadow-xl transition-shadow">
                <svg className="w-5 h-5 text-primary ml-1" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M8 5v14l11-7z"/>
                </svg>
              </div>
              <span className="font-medium">Watch Demo</span>
            </button>
          </div>

          {/* Social proof */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-6 text-gray-600 px-4" style={{animationDelay: '0.8s'}}>
            <div className="flex items-center gap-2">
              <div className="flex text-yellow-400">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 fill-current" />
                ))}
              </div>
              <span className="text-sm font-medium whitespace-nowrap text-gray-700">4.9/5 rating</span>
            </div>

            <div className="flex items-center gap-2">
              <Users className="w-5 h-5 text-primary" />
              <span className="text-sm font-medium text-gray-700">10,000+ creators</span>
            </div>

            <div className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-primary" />
              <span className="text-sm font-medium text-gray-700">#1 Growth Tool</span>
            </div>
          </div>
        </div>

        {/* Demo Cards Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 md:gap-8 max-w-6xl mx-auto px-4">
          {/* Left Demo Card */}
          <div className="bg-primary/10 rounded-2xl border border-gray-200 overflow-hidden">
            <div className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                  <span className="text-lg">🤖</span>
                </div>
                <div>
                  <h3 className="font-bold text-gray-900">Easy To Customize Your Strategy</h3>
                  <div className="flex text-yellow-400 text-sm">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-current" />
                    ))}
                  </div>
                </div>
              </div>
              <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                Follow people who are subscribed to other creators and registered on OnlyFans just recently. Be one of the first accounts they&apos;ll ever see.
              </p>
              <a
                href="https://app.ofautofollower.com/register"
                target="_blank"
                rel="noopener noreferrer"
                className="text-primary font-medium text-sm flex items-center gap-2 hover:gap-3 transition-all"
              >
                Sign Up Now
                <ChevronRight className="w-4 h-4" />
              </a>
            </div>
          </div>

          {/* Right Demo Card */}
          <div className="bg-primary/10 rounded-2xl border border-gray-200 overflow-hidden">
            <div className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                  <span className="text-lg">💬</span>
                </div>
                <div>
                  <h3 className="font-bold text-gray-900">Smart Messaging</h3>
                  <div className="flex text-yellow-400 text-sm">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-current" />
                    ))}
                  </div>
                </div>
              </div>
              <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                Personalized mass messages that show up in the #1 position in your fan&apos;s chat inbox. Gain unfair advantage over other creators.
              </p>
              <a
                href="https://app.ofautofollower.com/register"
                target="_blank"
                rel="noopener noreferrer"
                className="text-primary font-medium text-sm flex items-center gap-2 hover:gap-3 transition-all"
              >
                Sign Up Now
                <ChevronRight className="w-4 h-4" />
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}